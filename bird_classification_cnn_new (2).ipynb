import os
import sys
import shutil
import pathlib
import natsort
import numpy as np
import pandas as pd
import tensorflow as tf
import matplotlib.pyplot as plt
import seaborn as sns
import keras
import os
from google.colab import drive
from PIL import Image
from tqdm import tqdm
from tensorflow.keras import regularizers
from tensorflow.keras.preprocessing import image
from tensorflow.keras.preprocessing.image import ImageDataGenerator
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import Conv2D, MaxPooling2D, Dropout, Flatten, Dense, BatchNormalization,GlobalAveragePooling2D
from tensorflow.keras.optimizers import Adam
from tensorflow.keras.callbacks import ModelCheckpoint, EarlyStopping

from sklearn.metrics import (
    accuracy_score,
    precision_score,
    recall_score,
    confusion_matrix,
    classification_report,
)

drive.mount('/content/drive')
!cp "/content/drive/MyDrive/dataset.zip" "/content/"

# Unzip the dataset
!unzip -q '/content/dataset.zip' -d '/content/'

# Define the main data directory
datasetDir = '/content/dataset/'

# show amount of data for each diseases type
print("amount data of Lonchura leucogastroides : ", len(os.listdir(datasetDir + 'train/Lonchura leucogastroides')))
print("amount data of Lonchura maja : ", len(os.listdir(datasetDir + 'train/Lonchura maja')))
print("amount data of Lonchura punctulata: ", len(os.listdir(datasetDir + 'train/Lonchura punctulata')))
print("amount data of Passer montanus : ", len(os.listdir(datasetDir + 'train/Passer montanus')))
print("amount data of unknown spesies : ", len(os.listdir(datasetDir + 'train/Unknown')))
print("ammount data of testing directory : ", len(os.listdir(datasetDir + 'test')))

# Definisikan kelas burung dan jumlah kelas berdasarkan struktur direktori training
train_data_dir = os.path.join(datasetDir, 'train')
bird_classes = []
num_classes = 0

if os.path.exists(train_data_dir):
    # Ambil daftar direktori (kelas) di dalam folder train
    bird_classes = [d for d in os.listdir(train_data_dir) if os.path.isdir(os.path.join(train_data_dir, d))]
    bird_classes.sort() # Urutkan nama kelas untuk pengindeksan yang konsisten
    num_classes = len(bird_classes)
    print(f"Terdeteksi {num_classes} kelas: {bird_classes}")

    # Tampilkan jumlah data untuk setiap jenis penyakit
    print("\nJumlah data untuk setiap kelas di direktori training:")
    total_train_images = 0 # Inisialisasi total train images di sini
    for class_name in bird_classes:
        class_path = os.path.join(train_data_dir, class_name)
        if os.path.exists(class_path):
            num_images = len([f for f in os.listdir(class_path) if os.path.isfile(os.path.join(class_path, f))])
            print(f"Jumlah data dari {class_name} : {num_images}")
            total_train_images += num_images # Tambahkan ke total

    # Tampilkan total gambar training setelah iterasi
    if total_train_images > 0:
        print(f"Jumlah total data di direktori training : {total_train_images}")
    else:
        print("\nTidak ada gambar ditemukan di direktori training.")

else:
    print(f"Error: Direktori data training tidak ditemukan di {train_data_dir}")


# Tampilkan jumlah data di direktori testing
test_data_dir = os.path.join(datasetDir, 'test')
total_test_images = 0
if os.path.exists(test_data_dir):
    # Periksa apakah direktori test mengandung subfolder (mengasumsikan itu adalah folder kelas)
    test_subdirs = [d for d in os.listdir(test_data_dir) if os.path.isdir(os.path.join(test_data_dir, d))]

    if test_subdirs:
        # Jika ada subfolder, hitung gambar di dalamnya
        print("\nJumlah data untuk setiap kelas di direktori testing:")
        for subdir in test_subdirs:
            subdir_path = os.path.join(test_data_dir, subdir)
            if os.path.exists(subdir_path):
                 num_images_in_subdir = len([f for f in os.listdir(subdir_path) if os.path.isfile(os.path.join(subdir_path, f))])
                 print(f"Jumlah data dari {subdir} : {num_images_in_subdir}")
                 total_test_images += num_images_in_subdir
    else:
        # Jika tidak ada subfolder, hitung gambar langsung di direktori test
        total_test_images = len([f for f in os.listdir(test_data_dir) if os.path.isfile(os.path.join(test_data_dir, f))])
        print(f"\nJumlah data di direktori testing (tidak ada subfolder terdeteksi) : {total_test_images}")

    if total_test_images > 0:
        print(f"Jumlah total data di direktori testing : {total_test_images}")
    else:
        print("\nTidak ada gambar ditemukan di direktori testing.")

else:
    print(f"\nPeringatan: Direktori testing tidak ditemukan di {test_data_dir}")

# Hitung dan tampilkan total gambar (train + test)
# Pastikan total_train_images sudah dihitung di blok if train_data_dir exists
if os.path.exists(train_data_dir) and os.path.exists(test_data_dir):
    # total_train_images sudah dihitung di atas
    print(f"\nTotal gambar dalam dataset (Train + Test): {total_train_images + total_test_images}")

# SAMPLE IMAGES VISUALIZATION
def show_sample_images_per_class(train_dir, class_names, samples_per_class=3):
    """Tampilkan sample gambar dari setiap kelas"""

    print("Menampilkan sample gambar per kelas...")

    n_classes = len(class_names)
    fig, axes = plt.subplots(n_classes, samples_per_class, figsize=(15, 3 * n_classes))

    if n_classes == 1:
        axes = axes.reshape(1, -1)

    for class_idx, class_name in enumerate(class_names):
        class_path = os.path.join(train_dir, class_name)

        if os.path.exists(class_path):
            image_files = [f for f in os.listdir(class_path)
                          if f.lower().endswith(('.png', '.jpg', '.jpeg'))]

            for sample_idx in range(min(samples_per_class, len(image_files))):
                if samples_per_class == 1:
                    ax = axes[class_idx]
                else:
                    ax = axes[class_idx, sample_idx]

                try:
                    img_path = os.path.join(class_path, image_files[sample_idx])
                    img = plt.imread(img_path)
                    ax.imshow(img)

                    if sample_idx == 0:  # Only show class name on first image
                        ax.set_title(f'{class_name}', fontsize=12, fontweight='bold')
                    else:
                        ax.set_title(f'Sample {sample_idx + 1}', fontsize=10)

                    ax.axis('off')

                except Exception as e:
                    ax.text(0.5, 0.5, f'Error\nloading\nimage', ha='center', va='center',
                           transform=ax.transAxes, fontsize=10)
                    ax.axis('off')

            # Hide unused subplots for this class
            for sample_idx in range(len(image_files), samples_per_class):
                if samples_per_class > 1:
                    axes[class_idx, sample_idx].axis('off')

    plt.suptitle('Sample Gambar per Kelas Burung', fontsize=16, fontweight='bold')
    plt.tight_layout()
    plt.savefig('sample_images_per_class.png', dpi=300, bbox_inches='tight')
    plt.show()

# Jalankan visualisasi sample images
show_sample_images_per_class(
    train_dir='/content/dataset/train',  # Sesuaikan dengan path Anda
    class_names=['Lonchura leucogastroides', 'Lonchura maja', 'Lonchura punctulata', 'Passer montanus', 'Unknown'],
    samples_per_class=3
)

# ===== DATA GENERATOR YANG DIPERBAIKI =====
# Data generator dengan augmentasi yang lebih seimbang untuk mencegah underfitting

train_datagen = ImageDataGenerator(
    rescale=1./255,
    rotation_range=10,      # Kurangi dari 15
    width_shift_range=0.05, # Kurangi dari 0.08
    height_shift_range=0.05,# Kurangi dari 0.08
    shear_range=0.02,       # Kurangi dari 0.05
    zoom_range=0.05,        # Kurangi dari 0.1
    horizontal_flip=True,
    vertical_flip=False,
    fill_mode='nearest',
    validation_split=0.3    # Lebih banyak data training
)

validation_datagen = ImageDataGenerator(
    rescale=1./255,
    validation_split=0.3    # Konsisten dengan train_datagen
)

# Ukuran batch diperbesar untuk stabilitas training
batch_size = 16
target_size = (224, 224)
data_dir = '/content/dataset/train'

# Generator training - Menunjuk ke subdirektori 'train'
train_generator = train_datagen.flow_from_directory(
    data_dir,
    target_size=target_size,
    batch_size=batch_size,
    class_mode='categorical',
    subset='training',
    shuffle=True,
)

# Generator validasi - Menunjuk ke subdirektori 'test'
validation_generator = validation_datagen.flow_from_directory(
    data_dir,
    target_size=target_size,
    batch_size=batch_size,
    class_mode='categorical',
    subset='validation',
    shuffle=False,
)

# Perbarui num_classes berdasarkan generator training
num_classes = train_generator.num_classes

print(f"\n✅ Generator data berhasil dibuat dengan konfigurasi optimal!")
print(f"📈 Sampel training: {train_generator.samples}")
print(f"📊 Sampel validasi: {validation_generator.samples}")
print(f"🎯 Jumlah kelas: {num_classes}")
print(f"📦 Ukuran batch: {batch_size}")
print(f"🖼️ Ukuran target: {target_size}")

# Rasio data training vs validasi
train_ratio = train_generator.samples / (train_generator.samples + validation_generator.samples)
val_ratio = validation_generator.samples / (train_generator.samples + validation_generator.samples)
print(f"\n📊 Rasio Data:")
print(f"   • Training: {train_ratio:.1%}")
print(f"   • Validasi: {val_ratio:.1%}")

def create_improved_model():
    """Model yang diperbaiki untuk mengatasi underfitting"""
    model = Sequential()

    # Block 1 - Mulai dengan filter yang cukup
    model.add(Conv2D(32, kernel_size=[3,3], padding='same', activation='relu',
                     input_shape=(*target_size, 3)))
    model.add(Conv2D(32, kernel_size=[3,3], padding='same', activation='relu'))
    model.add(BatchNormalization())  # BatchNorm setelah conv block
    model.add(MaxPooling2D(pool_size=[2,2]))  # Pool size diperkecil
    model.add(Dropout(0.25))

    # Block 2 - Tingkatkan kapasitas
    model.add(Conv2D(64, kernel_size=[3,3], padding='same', activation='relu'))
    model.add(Conv2D(64, kernel_size=[3,3], padding='same', activation='relu'))
    model.add(BatchNormalization())
    model.add(MaxPooling2D(pool_size=[2,2]))
    model.add(Dropout(0.25))

    # Block 3 - Fitur kompleks
    model.add(Conv2D(128, kernel_size=[3,3], padding='same', activation='relu'))
    model.add(Conv2D(128, kernel_size=[3,3], padding='same', activation='relu'))
    model.add(BatchNormalization())
    model.add(MaxPooling2D(pool_size=[2,2]))
    model.add(Dropout(0.3))

    # Block 4 - High-level features
    model.add(Conv2D(256, kernel_size=[3,3], padding='same', activation='relu'))
    model.add(Conv2D(256, kernel_size=[3,3], padding='same', activation='relu'))
    model.add(BatchNormalization())
    model.add(MaxPooling2D(pool_size=[2,2]))
    model.add(Dropout(0.3))

    # Classifier - Progressive learning
    model.add(Flatten())
    model.add(Dense(512, activation='relu', kernel_regularizer=regularizers.l2(0.001)))
    model.add(BatchNormalization())
    model.add(Dropout(0.4))  # Kurangi dropout untuk mengurangi underfitting

    model.add(Dense(256, activation='relu', kernel_regularizer=regularizers.l2(0.001)))
    model.add(BatchNormalization())
    model.add(Dropout(0.4))

    model.add(Dense(num_classes, activation='softmax'))

    # Compile dengan learning rate yang tepat
    model.compile(
        optimizer=Adam(learning_rate=0.001),  # Learning rate standar
        loss='categorical_crossentropy',
        metrics=['accuracy']
    )

    print('Improved Model Created')
    model.summary()
    return model

# Ganti model lama dengan yang diperbaiki
model = create_improved_model()

#PERSIAPAN GAMBAR UNTUK VISUALISASI
import random

# Ambil satu path gambar acak dari direktori test
try:
    # Direktori data test
    test_data_dir = '/content/dataset/test'
    # Ambil nama kelas secara acak
    random_class = random.choice(os.listdir(test_data_dir))
    class_path = os.path.join(test_data_dir, random_class)
    # Ambil nama file gambar secara acak dari kelas tersebut
    random_image_file = random.choice(os.listdir(class_path))
    image_path = os.path.join(class_path, random_image_file)

    print(f"Gambar yang dipilih untuk visualisasi: {image_path}")

    # Load dan proses gambar
    target_size = (224, 224) # Pastikan target_size sesuai dengan yang digunakan saat training
    img = image.load_img(image_path, target_size=target_size)
    img_array = image.img_to_array(img)
    img_array_expanded = np.expand_dims(img_array, axis=0) # Shape jadi (1, 224, 224, 3)

    # Tampilkan gambar sampel
    plt.imshow(img)
    plt.title(f"Gambar Sampel: {random_class}")
    plt.axis('off')
    plt.show()

except Exception as e:
    print(f"Gagal memuat gambar sampel. Error: {e}")
    print("Pastikan path direktori test sudah benar dan berisi gambar.")

#MEMBUAT MODEL UNTUK EKSTRAKSI FITUR

# Ambil indeks dari setiap jenis lapisan yang ingin divisualisasikan
# Sesuaikan indeks ini jika Anda mengubah arsitektur model Anda
conv_layer_indices = [i for i, layer in enumerate(model.layers) if 'conv2d' in layer.name]
maxpool_layer_indices = [i for i, layer in enumerate(model.layers) if 'max_pooling2d' in layer.name]

# Ambil output dari setiap lapisan tersebut
conv_outputs = [model.layers[i].output for i in conv_layer_indices]
maxpool_outputs = [model.layers[i].output for i in maxpool_layer_indices]

# Buat model baru yang inputnya sama dengan model asli, tetapi outputnya adalah peta fitur
feature_map_conv_model = tf.keras.Model(inputs=model.inputs, outputs=conv_outputs)
feature_map_maxpool_model = tf.keras.Model(inputs=model.inputs, outputs=maxpool_outputs)

print("Model untuk ekstraksi fitur berhasil dibuat.")

#MENJALANKAN PREDIKSI & MENDAPATKAN PETA FITUR

# Pastikan gambar sampel (img_array_expanded) sudah ada dari Blok 1
try:
    # Jalankan prediksi pada gambar untuk mendapatkan peta fitur
    conv_feature_maps = feature_map_conv_model.predict(img_array_expanded)
    maxpool_feature_maps = feature_map_maxpool_model.predict(img_array_expanded)

    print("Peta fitur berhasil diekstrak.")
    # Contoh: Tampilkan bentuk output dari lapisan konvolusi pertama
    print(f"Bentuk output Conv Layer pertama: {conv_feature_maps[0].shape}")
    print(f"Bentuk output MaxPool Layer pertama: {maxpool_feature_maps[0].shape}")
except NameError:
    print("Variabel 'img_array_expanded' tidak ditemukan. Pastikan Blok 1 sudah dijalankan.")
except Exception as e:
    print(f"Terjadi error saat ekstraksi fitur: {e}")

#FUNGSI UNTUK MENAMPILKAN PETA FITUR

def show_feature_maps(layer_names, feature_maps, features_per_row=8, max_features_to_show=None):
    """
    Menampilkan visualisasi peta fitur (feature maps) dari lapisan-lapisan CNN.
    Membatasi jumlah peta fitur yang ditampilkan per lapisan.
    """
    for layer_name, feature_map in zip(layer_names, feature_maps):
        # feature_map memiliki shape (1, tinggi, lebar, jumlah_filter)
        num_features = feature_map.shape[-1]

        # Tentukan berapa banyak fitur yang akan ditampilkan
        display_features = num_features
        if max_features_to_show is not None and max_features_to_show < num_features:
            display_features = max_features_to_show

        if display_features == 0:
            print(f"Tidak ada peta fitur untuk ditampilkan untuk lapisan: {layer_name}")
            continue

        # Atur ukuran grid untuk subplot
        n_cols = features_per_row
        n_rows = (display_features + n_cols - 1) // n_cols # Menghitung jumlah baris yang dibutuhkan

        plt.figure(figsize=(n_cols * 1.5, n_rows * 1.5))
        plt.suptitle(f"Peta Fitur dari Layer: {layer_name} (Shape: {feature_map.shape})", fontsize=16)

        for i in range(display_features):
            ax = plt.subplot(n_rows, n_cols, i + 1)
            # Tampilkan setiap channel dari peta fitur
            plt.imshow(feature_map[0, :, :, i], cmap='viridis')
            ax.set_xticks([])
            ax.set_yticks([])

        plt.tight_layout()
        plt.show()

print("Fungsi 'show_feature_maps' berhasil didefinisikan.")

#VISUALISASI HASIL
# Pastikan semua variabel dari blok sebelumnya sudah ada
try:
    # Ambil nama lapisan untuk judul plot
    conv_layer_names = [model.layers[i].name for i in conv_layer_indices]
    maxpool_layer_names = [model.layers[i].name for i in maxpool_layer_indices]

    # Tentukan jumlah maksimum peta fitur yang ingin ditampilkan per lapisan
    max_features = 8 # Ubah angka ini sesuai keinginan Anda

    print("\nVisualisasi Peta Fitur dari Lapisan Konvolusi (Conv2D):")
    show_feature_maps(conv_layer_names, conv_feature_maps, features_per_row=8, max_features_to_show=max_features)

    print("\nVisualisasi Peta Fitur dari Lapisan Pooling (MaxPooling2D):")
    show_feature_maps(maxpool_layer_names, maxpool_feature_maps, features_per_row=8, max_features_to_show=max_features)

except NameError:
    print("Variabel tidak ditemukan. Pastikan Blok 1, 2, dan 3 sudah dijalankan.")
except Exception as e:
    print(f"Terjadi error saat visualisasi: {e}")

## ===== BLOK CALLBACK YANG DISEMPURNAKAN =====
from tensorflow.keras.callbacks import ModelCheckpoint, EarlyStopping, ReduceLROnPlateau

# Buat direktori untuk menyimpan model jika belum ada
model_save_dir = '/content/saved_models/'
if not os.path.exists(model_save_dir):
    os.makedirs(model_save_dir)

# Path lengkap untuk file model terbaik
best_model_path = os.path.join(model_save_dir, 'best_bird_classification_model.h5')

checkpoint = ModelCheckpoint(
    filepath=best_model_path,
    monitor='val_loss',      # Memantau validation loss
    mode='min',              # Mode 'min' karena kita ingin loss sekecil mungkin
    save_best_only=True,     # Hanya menyimpan model jika 'val_loss' membaik
    verbose=1                # Memberi notifikasi saat model disimpan
)

# Mencegah overfitting dan menghemat waktu.
early_stop = EarlyStopping(
    monitor='val_loss',
    patience=10,
    verbose=1,
    restore_best_weights=True # Mengembalikan bobot dari epoch dengan val_loss terbaik
)

# Inisialisasi ReduceLROnPlateau dan tetapkan ke variabel
reduce_lr = ReduceLROnPlateau(
        monitor='val_accuracy',  # Monitor accuracy
        factor=0.5,              # Kurangi LR lebih gradual
        patience=7,              # Patience lebih besar
        min_lr=0.00001,
        verbose=1
)

# Gabungkan semua callback ke dalam satu list
callbacks = [checkpoint, early_stop, reduce_lr] # Gunakan variabel reduce_lr di sini

print(f"ModelCheckpoint akan menyimpan model terbaik di: {best_model_path}")

# ===== TRAINING MODEL =====
# Hitung langkah per epoch
steps_per_epoch = max(1, train_generator.samples // batch_size)
validation_steps = max(1, validation_generator.samples // batch_size)

# Latih model
history = model.fit(
    train_generator,
    steps_per_epoch=steps_per_epoch,
    epochs=60,
    validation_data=validation_generator,
    validation_steps=validation_steps,
    callbacks=callbacks
)

# ===== VISUALISASI RIWAYAT TRAINING =====
print("📊 Memvisualisasikan riwayat training...")

# Ekstrak riwayat training
acc = history.history['accuracy']
val_acc = history.history['val_accuracy']
loss = history.history['loss']
val_loss = history.history['val_loss']
epochs_range = range(len(acc))

# Buat subplot
plt.figure(figsize=(15, 5))

# Plot akurasi
plt.subplot(1, 3, 1)
plt.plot(epochs_range, acc, label='Akurasi Training', linewidth=2)
plt.plot(epochs_range, val_acc, label='Akurasi Validasi', linewidth=2)
plt.title('Akurasi Training dan Validasi', fontsize=14, fontweight='bold')
plt.xlabel('Epochs')
plt.ylabel('Akurasi')
plt.legend()
plt.grid(True, alpha=0.3)

# Plot loss
plt.subplot(1, 3, 2)
plt.plot(epochs_range, loss, label='Loss Training', linewidth=2)
plt.plot(epochs_range, val_loss, label='Loss Validasi', linewidth=2)
plt.title('Loss Training dan Validasi', fontsize=14, fontweight='bold')
plt.xlabel('Epochs')
plt.ylabel('Loss')
plt.legend()
plt.grid(True, alpha=0.3)

# Plot laju pembelajaran (jika tersedia)
plt.subplot(1, 3, 3)
if 'lr' in history.history:
    plt.plot(epochs_range, history.history['lr'], label='Laju Pembelajaran', linewidth=2, color='orange')
    plt.title('Jadwal Laju Pembelajaran', fontsize=14, fontweight='bold')
    plt.xlabel('Epochs')
    plt.ylabel('Laju Pembelajaran')
    plt.yscale('log')
    plt.legend()
    plt.grid(True, alpha=0.3)
else:
    # Tampilkan metrik akhir sebagai gantinya
    final_acc = acc[-1]
    final_val_acc = val_acc[-1]
    final_loss = loss[-1]
    final_val_loss = val_loss[-1]

    metrics_text = f"Metrik Akhir:\n\n"
    metrics_text += f"Akurasi Training: {final_acc:.4f}\n"
    metrics_text += f"Akurasi Validasi: {final_val_acc:.4f}\n\n"
    metrics_text += f"Loss Training: {final_loss:.4f}\n"
    metrics_text += f"Loss Validasi: {final_val_loss:.4f}"

    plt.text(0.1, 0.5, metrics_text, fontsize=12,
             bbox=dict(boxstyle="round,pad=0.3", facecolor="lightblue", alpha=0.7),
             transform=plt.gca().transAxes)
    plt.title('Metrik Training Akhir', fontsize=14, fontweight='bold')
    plt.axis('off')

plt.tight_layout()
plt.show()

# Simpan riwayat training
results_dir = './results'
os.makedirs(results_dir, exist_ok=True)

# Simpan sebagai CSV
history_df = pd.DataFrame(history.history)
history_df.to_csv(os.path.join(results_dir, 'training_history.csv'), index=False)

print(f"Riwayat training disimpan di: {os.path.join(results_dir, 'training_history.csv')}")

# ===== CROSS-VALIDATION K-FOLD =====
print("⏳ Memulai Cross-Validation K-Fold...")

from sklearn.model_selection import KFold
import numpy as np

# Definisikan jumlah lipatan (folds)
n_splits = 5 # Anda bisa menyesuaikan angka ini

# Siapkan data untuk K-Fold
# Perlu mendapatkan path file dan label dari direktori training
train_data_dir = os.path.join(datasetDir, 'train') # Menggunakan datasetDir sebagaimana diklarifikasi
if not os.path.exists(train_data_dir):
    print(f"Error: Direktori data training tidak ditemukan di {train_data_dir}. Tidak dapat melakukan cross-validation.")
else:
    # Dapatkan semua path gambar dan label yang sesuai dari direktori training
    all_image_paths = []
    all_image_labels = []
    print(f"Mengumpulkan path gambar dan label dari: {train_data_dir}")

    # Pastikan bird_classes didefinisikan dan berisi nama-nama kelas
    if 'bird_classes' not in locals() or not bird_classes:
        print("Error: 'bird_classes' tidak didefinisikan. Harap jalankan sel konfigurasi data terlebih dahulu.")
    else:
        class_names_list = bird_classes # Gunakan daftar nama kelas

        for class_name in class_names_list:
            class_path = os.path.join(train_data_dir, class_name)
            if os.path.exists(class_path):
                image_files = [os.path.join(class_path, f) for f in os.listdir(class_path)
                               if f.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp', '.tiff'))]
                all_image_paths.extend(image_files)
                all_image_labels.extend([class_name] * len(image_files)) # Tetapkan label untuk setiap gambar

        if not all_image_paths:
             print("Tidak ada gambar ditemukan di direktori training.")
        else:
            # Petakan label string ke indeks integer
            label_to_index = {name: i for i, name in enumerate(class_names_list)}
            all_image_indices = np.array([label_to_index[label] for label in all_image_labels])
            all_image_paths = np.array(all_image_paths)

            print(f"Ditemukan {len(all_image_paths)} total gambar training.")

            # Inisialisasi KFold
            kf = KFold(n_splits=n_splits, shuffle=True, random_state=42) # Menambahkan random_state untuk reproduksibilitas

            # Simpan hasil dari setiap fold
            fold_accuracies = []
            fold_losses = []

            print(f"\nMenjalankan Cross-Validation {n_splits}-Fold...")

            # Loop melalui setiap fold
            for fold, (train_index, val_index) in enumerate(kf.split(all_image_paths, all_image_indices)):
                print(f"\n--- Fold {fold+1}/{n_splits} ---")

                # Dapatkan data train dan validasi untuk fold ini
                X_train_fold, X_val_fold = all_image_paths[train_index], all_image_paths[val_index]
                y_train_fold_indices, y_val_fold_indices = all_image_indices[train_index], all_image_indices[val_index]

                # Ubah label integer menjadi one-hot encoded (diperlukan untuk categorical_crossentropy)
                y_train_fold_one_hot = tf.keras.utils.to_categorical(y_train_fold_indices, num_classes=num_classes)
                y_val_fold_one_hot = tf.keras.utils.to_categorical(y_val_fold_indices, num_classes=num_classes)


                # Buat Data Generator untuk fold ini
                # Catatan: Kita perlu membuat generator baru untuk setiap fold untuk menggunakan subset data yang benar
                fold_train_datagen = ImageDataGenerator(
                    rescale=1./255,
                    rotation_range=15,
                    width_shift_range=0.1,
                    height_shift_range=0.1,
                    shear_range=0.1,
                    zoom_range=0.15,
                    horizontal_flip=True,
                    vertical_flip=False,
                    fill_mode='nearest'
                )

                fold_val_datagen = ImageDataGenerator(rescale=1./255)

                # Gunakan flow_from_dataframe atau yang serupa jika pathing langsung kompleks dengan indeks KFold
                # Pendekatan sederhana adalah memuat gambar secara manual untuk dataset yang lebih kecil atau menggunakan tf.data pipeline untuk yang besar
                # Mengingat struktur saat ini, mari kita coba pendekatan sederhana dengan meneruskan array secara langsung
                # Ini memerlukan pemuatan gambar ke dalam memori, yang mungkin menjadi masalah untuk dataset yang sangat besar.
                # Untuk dataset besar, tf.data pipeline akan lebih cocok, tetapi memerlukan perubahan kode yang signifikan.
                # Untuk saat ini, mari kita asumsikan ukuran dataset memungkinkan pemuatan subset ke dalam memori untuk setiap fold.

                print("Memuat gambar untuk fold saat ini...")
                X_train_images_fold = np.array([image.img_to_array(image.load_img(p, target_size=target_size)) for p in X_train_fold])
                X_val_images_fold = np.array([image.img_to_array(image.load_img(p, target_size=target_size)) for p in X_val_fold])

                # Rescale gambar
                X_train_images_fold /= 255.0
                X_val_images_fold /= 255.0

                # Buat ulang arsitektur model untuk setiap fold untuk memastikan awal yang baru
                # (Bobot tidak dibagikan antar fold)
                # Anda mungkin ingin menyimpan arsitektur model secara terpisah dan memuatnya di sini
                # Untuk saat ini, mari kita asumsikan definisi model tersedia secara global atau dapat dibuat ulang.
                # (Ini mengasumsikan sel kode arsitektur model sudah dijalankan)

                # Buat ulang model menggunakan definisi Sequential dari sel REo-2V8bBKGL
                # Ini adalah placeholder - idealnya, definisikan fungsi untuk membuat model
                print("Membuat ulang model untuk fold saat ini...")
                try:
                    fold_model = Sequential([
                        # Blok 1
                        Conv2D(32, (3, 3), activation='relu', padding='same', input_shape=(*target_size, 3)),
                        Conv2D(32, (3, 3), activation='relu', padding='same'),
                        MaxPooling2D((2, 2)),
                        Dropout(0.25),

                        # Blok 2
                        Conv2D(64, (3, 3), activation='relu', padding='same'),
                        Conv2D(64, (3, 3), activation='relu', padding='same'),
                        MaxPooling2D((2, 2)),
                        Dropout(0.25),

                        # Blok 3
                        Conv2D(128, (3, 3), activation='relu', padding='same'),
                        Conv2D(128, (3, 3), activation='relu', padding='same'),
                        MaxPooling2D((2, 2)),
                        Dropout(0.25),

                        # Blok 4
                        Conv2D(256, (3, 3), activation='relu', padding='same'),
                        Conv2D(256, (3, 3), activation='relu', padding='same'),
                        MaxPooling2D((2, 2)),
                        Dropout(0.25),

                        # Classifier
                        Flatten(),
                        Dense(512, activation='relu'),
                        Dropout(0.5),
                        Dense(256, activation='relu'),
                        Dropout(0.5),
                        Dense(num_classes, activation='softmax') # Gunakan num_classes yang didefinisikan sebelumnya
                    ])

                    # Kompilasi model
                    fold_model.compile(
                        optimizer=Adam(learning_rate=0.001),
                        loss='categorical_crossentropy',
                        metrics=['accuracy']
                    )
                    print("Model berhasil dibuat ulang dan dikompilasi.")

                except Exception as e:
                    print(f"Error saat membuat ulang model untuk fold {fold+1}: {e}")
                    continue # Lewati fold ini jika pembuatan ulang model gagal


                # Latih model pada data training fold
                print("Melatih model untuk fold saat ini...")
                # Gunakan fit() dengan array data
                history_fold = fold_model.fit(
                    fold_train_datagen.flow(X_train_images_fold, y_train_fold_one_hot, batch_size=batch_size),
                    epochs=10, # Epoch dikurangi untuk CV lebih cepat, sesuaikan sesuai kebutuhan
                    validation_data=fold_val_datagen.flow(X_val_images_fold, y_val_fold_one_hot, batch_size=batch_size),
                    verbose=0 # Atur ke 1 untuk melihat progres training per epoch
                )
                print(f"Training selesai untuk fold {fold+1}.")

                # Evaluasi model pada data validasi fold
                print(f"Mengevaluasi model untuk fold {fold+1}...")
                loss_fold, accuracy_fold = fold_model.evaluate(fold_val_datagen.flow(X_val_images_fold, y_val_fold_one_hot, batch_size=batch_size), verbose=0)
                print(f"Fold {fold+1} - Loss: {loss_fold:.4f}, Akurasi: {accuracy_fold:.4f}")

                fold_losses.append(loss_fold)
                fold_accuracies.append(accuracy_fold)

                # Opsional: Bersihkan sesi untuk membebaskan memori, terutama untuk banyak fold
                tf.keras.backend.clear_session()


            # Laporkan hasil rata-rata di semua fold
            print("\n--- Ringkasan Cross-Validation ---")
            print(f"Akurasi Validasi Rata-rata: {np.mean(fold_accuracies):.4f} (+/- {np.std(fold_accuracies):.4f})")
            print(f"Loss Validasi Rata-rata: {np.mean(fold_losses):.4f} (+/- {np.std(fold_losses):.4f})")
            print("✅ Cross-Validation K-Fold selesai.")


# To delete a directory and its contents:
!rm -r /content/dataset.zip

# Be careful when using rm, as deleted files cannot be recovered.
# Uncomment the line you want to use and replace the path with the actual path to your file or directory.